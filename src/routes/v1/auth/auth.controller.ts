import { HTTPException } from "hono/http-exception";
import { AuthService } from "./auth.service";

/**
 * Sign up with email and password
 * Controller orchestrates the complete user onboarding process by wiring together service functions:
 * 1. Create user account
 * 2. Create organization (with automatic membership via Better Auth)
 * 3. Create user profile
 */
export const signUpEmail = async (c: any) => {
  try {
    const body = c.req.valid("json");

    // Step 1: Create user account with Better Auth
    const authData = await AuthService.createUserAccount({
      name: body.name,
      email: body.email,
      password: body.password,
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
      image: body.image,
      callbackURL: body.callbackURL,
      rememberMe: body.rememberMe,
    });

    // Step 2: Create organization
    const organizationId = await AuthService.createOrganization(authData.user.id, {
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
    });

    // Step 3: Create user profile (membership is automatically created by Better Auth)
    await AuthService.createUserProfile(authData.user.id, organizationId, {
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
    });

    // Step 4: Get enriched data for response
    const enrichedData = await AuthService.getEnrichedAuthData(authData.user.id, authData.token);

    // Step 5: Build final response
    const result = {
      token: authData.token || null,
      user: authData.user,
      session: authData.session || enrichedData.session,
      organization: enrichedData.organization,
      profile: enrichedData.profile,
    };

    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Sign up controller error:", error);
    throw new HTTPException(500, { message: "Failed to create user account" });
  }
};

/**
 * Sign in with email and password
 */
export const signInEmail = async (c: any) => {
  try {
    const body = c.req.valid("json");
    
    // Validate required fields
    if (!body.email || !body.password) {
      throw new HTTPException(400, { 
        message: "Email and password are required" 
      });
    }

    // Call auth service
    const result = await AuthService.signInEmail({
      email: body.email,
      password: body.password,
      callbackURL: body.callbackURL,
      rememberMe: body.rememberMe,
    });

    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }
    
    console.error("Sign in controller error:", error);
    throw new HTTPException(500, { message: "Failed to authenticate user" });
  }
};
