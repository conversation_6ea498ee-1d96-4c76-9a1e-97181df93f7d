import { auth } from "@/lib/auth";
import db from "@/db";
import { userProfiles, organization, member } from "@/db/schema";
import { eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import { randomUUID } from "crypto";

export interface SignUpData {
  name: string;
  email: string;
  password: string;
  companyName: string;
  companyType: "individual" | "team" | "firm";
  firstName: string;
  lastName: string;
  phone?: string;
  licenseNumber?: string;
  website?: string;
  businessAddress?: string;
  marketingConsent?: boolean;
  termsAccepted: boolean;
  image?: string;
  callbackURL?: string;
  rememberMe?: boolean;
}

export interface SignInData {
  email: string;
  password: string;
  callbackURL?: string;
  rememberMe?: boolean;
}

export interface OrganizationData {
  companyName: string;
  companyType: "individual" | "team" | "firm";
  firstName: string;
  lastName: string;
  phone?: string;
  licenseNumber?: string;
  website?: string;
  businessAddress?: string;
  marketingConsent?: boolean;
  termsAccepted: boolean;
}

export interface AuthResponse {
  token: string;
  user: any;
  session: any;
  organization: any;
  profile: any;
}

export class AuthService {


  /**
   * Sign in a user with email and password
   */
  static async signInEmail(data: SignInData): Promise<AuthResponse> {
    try {
      // Call Better Auth sign in API
      const response = await auth.api.signInEmail({
        body: {
          email: data.email,
          password: data.password,
          callbackURL: data.callbackURL,
          rememberMe: data.rememberMe,
        },
        asResponse: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new HTTPException(response.status as any, {
          message: errorData.message || "Sign in failed"
        });
      }

      const authData = await response.json();

      // Get additional data
      const enrichedData = await this.getEnrichedAuthData(authData.user.id, authData.token);

      return {
        token: authData.token || null,
        user: authData.user,
        session: authData.session || enrichedData.session,
        organization: enrichedData.organization,
        profile: enrichedData.profile,
      };
    } catch (error: any) {
      if (error instanceof HTTPException) {
        throw error;
      }
      
      // Handle specific Better Auth errors
      if (error.message?.includes("Invalid credentials")) {
        throw new HTTPException(401, { message: "Invalid email or password" });
      }
      
      console.error("Sign in error:", error);
      throw new HTTPException(500, { message: "Internal server error during sign in" });
    }
  }

  /**
   * Get enriched auth data including session, organization, and profile
   * Uses Better Auth APIs when possible, falls back to direct DB queries
   */
  static async getEnrichedAuthData(userId: string, token: string | null) {
    try {
      let sessionRecord = null;
      let organizationRecord = null;
      let profileRecord = null;

      // Get session data using Better Auth API if token is available
      if (token) {
        try {
          const sessionResponse = await auth.api.getSession({
            headers: new Headers({
              'Authorization': `Bearer ${token}`,
              'Cookie': `better-auth.session_token=${token}`
            })
          });

          if (sessionResponse?.session) {
            sessionRecord = sessionResponse.session;
          }
        } catch (error) {
          console.log('Could not get session via Better Auth API, token may be invalid');
        }
      }

      // Get user's organization using direct DB query since Better Auth API requires auth
      // This is reliable since the organization was just created by hooks
      try {
        const membershipData = await db
          .select({
            organization: organization,
            member: member,
          })
          .from(member)
          .innerJoin(organization, eq(member.organizationId, organization.id))
          .where(eq(member.userId, userId))
          .limit(1);

        organizationRecord = membershipData[0]?.organization || null;
      } catch (error) {
        console.log('Could not get organization from database:', error);
      }

      // Get user profile using direct DB query
      try {
        const profileData = await db
          .select()
          .from(userProfiles)
          .where(eq(userProfiles.userId, userId))
          .limit(1);

        profileRecord = profileData[0] || null;
      } catch (error) {
        console.log('Could not get user profile:', error);
      }

      return {
        session: sessionRecord,
        organization: organizationRecord,
        profile: profileRecord,
      };
    } catch (error) {
      console.error("Error getting enriched auth data:", error);
      return {
        session: null,
        organization: null,
        profile: null,
      };
    }
  }

  /**
   * Create a new organization using Better Auth plugin
   */
  static async createOrganization(userId: string, orgData: OrganizationData): Promise<string> {
    try {
      // Generate organization name and slug
      const organizationName = orgData.companyName;
      const slug = organizationName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

      // Create metadata
      const metadata = {
        plan: "trial",
        status: "trial",
        companyType: orgData.companyType,
        phone: orgData.phone,
        licenseNumber: orgData.licenseNumber,
        website: orgData.website,
        businessAddress: orgData.businessAddress,
        marketingConsent: orgData.marketingConsent,
        termsAccepted: orgData.termsAccepted,
      };

      // Use Better Auth organization plugin
      const organizationData = await auth.api.createOrganization({
        body: {
          name: organizationName,
          slug: slug,
          logo: undefined,
          metadata,
          userId: userId, // server-only
          keepCurrentActiveOrganization: false,
        },
      });

      if (!organizationData) {
        throw new HTTPException(500, { message: "Failed to create organization - no data returned" });
      }

      console.log('Debug - Created organization with ID:', organizationData.id);
      return organizationData.id;
    } catch (error) {
      console.error('Error creating organization:', error);
      if (error instanceof HTTPException) {
        throw error;
      }
      throw new HTTPException(500, { message: "Failed to create organization" });
    }
  }

  /**
   * Create a membership for a user in an organization
   */
  static async createMembership(userId: string, organizationId: string, role: string = 'owner'): Promise<void> {
    try {
      await db.insert(member).values({
        id: randomUUID(),
        organizationId,
        userId,
        role,
        createdAt: new Date(),
      });

      console.log('Debug - Created membership for user as', role);
    } catch (error) {
      console.error('Error creating membership:', error);
      throw new HTTPException(500, { message: "Failed to create membership" });
    }
  }

  /**
   * Create a user profile
   */
  static async createUserProfile(userId: string, organizationId: string, orgData: OrganizationData): Promise<void> {
    try {
      const displayName = `${orgData.firstName} ${orgData.lastName}`;

      await db.insert(userProfiles).values({
        userId,
        organizationId,
        isActive: true,
        joinedAt: new Date().toISOString(),
        displayName,
        firstName: orgData.firstName,
        lastName: orgData.lastName,
        phone: orgData.phone,
        licenseNumber: orgData.licenseNumber,
        website: orgData.website,
        businessAddress: orgData.businessAddress,
        marketingConsent: orgData.marketingConsent || false,
        termsAccepted: orgData.termsAccepted,
        termsAcceptedAt: orgData.termsAccepted ? new Date().toISOString() : null,
      });

      console.log('Debug - Created user profile successfully');
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw new HTTPException(500, { message: "Failed to create user profile" });
    }
  }

  /**
   * Create user account with Better Auth only (no organization setup)
   */
  static async createUserAccount(data: SignUpData): Promise<any> {
    try {
      const response = await auth.api.signUpEmail({
        body: {
          name: data.name,
          email: data.email,
          password: data.password,
          image: data.image,
          callbackURL: data.callbackURL,
          rememberMe: data.rememberMe,
        },
        asResponse: true,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new HTTPException(response.status as any, {
          message: errorData.message || "Sign up failed"
        });
      }

      const authData = await response.json();
      return authData;
    } catch (error: any) {
      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific Better Auth errors
      if (error.message?.includes("User already exists")) {
        throw new HTTPException(409, { message: "User with this email already exists" });
      }

      console.error("User account creation error:", error);
      throw new HTTPException(500, { message: "Failed to create user account" });
    }
  }
}
